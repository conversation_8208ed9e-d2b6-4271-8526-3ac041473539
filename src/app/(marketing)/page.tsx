import { headers } from "next/headers";
import { redirect } from "next/navigation";
import Blog from "@/components/marketing/blog";
import Features from "@/components/marketing/features";
import Footer from "@/components/marketing/footer";
import Hero from "@/components/marketing/hero";
import { JackProm<PERSON> } from "@/components/marketing";
import People from "@/components/marketing/people";
import { auth } from "@/lib/auth";

export default async function HomePage() {
  // Check session server-side
  const session = await auth.api.getSession({ headers: await headers() });

  if (session) {
    redirect("/dashboard");
  }

  // JSON-LD structured data for better SEO
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: "TradeCrews",
    url: "https://tradecrews.com",
    logo: "https://tradecrews.com/logo.png",
    description:
      "Connecting homeowners with trusted trade professionals for home improvement and construction projects.",
    sameAs: [
      "https://twitter.com/tradecrews",
      "https://facebook.com/tradecrews",
      "https://linkedin.com/company/tradecrews",
    ],
  };

  return (
    <main>
      {/* Add JSON-LD structured data */}
      <script
        type="application/ld+json"
        // biome-ignore lint/security/noDangerouslySetInnerHtml: JSON-LD structured data
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <Hero session={session} />
      <Features />
      <JackPromo />
      <Blog />
      <People />
      <Footer />
    </main>
  );
}